{"name": "cloudstudio-runner", "version": "1.0.0", "description": "Playwright automation for CloudStudio WebIDE", "main": "index.js", "scripts": {"login": "node login.js", "execute": "node execute-command.js", "scheduler": "node scheduler.js", "test-scheduler": "node test-scheduler.js"}, "keywords": ["playwright", "automation", "webide"], "author": "", "license": "ISC", "type": "module", "dependencies": {"playwright": "^1.52.0"}}